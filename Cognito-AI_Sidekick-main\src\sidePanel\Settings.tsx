import { useConfig } from './ConfigContext';
import { ConnectCard } from './ConnectCard';
import { PageContextCard } from './PageContextCard';
import { ModelSettingsCard } from './ModelSettingsCard';
import { PersonaCard } from './PersonaCard';
import { WebSearchCard } from './WebSearchCard';
import { Button } from '@/components/ui/button';
import { cn } from 'src/background/util';
import { useState } from 'react';
import { FiX } from 'react-icons/fi';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

interface SettingsProps {
  onClose?: () => void;
}

export const Settings = ({ onClose }: SettingsProps = {}) => {
  const { config } = useConfig();
  const [showWarning, setShowWarning] = useState(!config?.models || config.models.length === 0);

  return (
    <div
      id="settings"
      className="relative z-[1] top-0 w-full h-full flex-1 flex-col overflow-y-auto overflow-x-hidden bg-background text-foreground px-6 pb-10 pt-6 scrollbar-hidden"
      >
      {/* Close button in top-right corner */}
      {onClose && (
        <div className="absolute top-4 right-4 z-10">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="text-foreground hover:bg-secondary rounded-lg p-2 h-8 w-8 flex items-center justify-center"
                onClick={onClose}
                aria-label="Close Settings"
              >
                <FiX size="18px" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="bottom" className="bg-popover text-popover-foreground border border-border">
              Close Settings
            </TooltipContent>
          </Tooltip>
        </div>
      )}
      <div className="flex justify-center mb-6">
        <div id="kofi-widget">
          <a href='https://ko-fi.com/T6T11G2CYS' target='_blank' rel="noopener noreferrer">
            <img
              height='36'
              style={{border: '0px', height: '36px'}}
              src='https://storage.ko-fi.com/cdn/kofi6.png?v=6'
              alt='Buy Me a Coffee at ko-fi.com'
            />
          </a>
        </div>
      </div>
      {showWarning && (
        <div className={cn(
          "mb-8 p-6",
          "rounded-xl",
          "bg-card border border-border shadow-sm",
          "text-foreground"
        )}>
          <div className="flex flex-col items-center gap-4">
            <h2 className="text-apple-title3 font-semibold text-center">Quick Setup Guide</h2>
            <div className="flex flex-col gap-4 w-full">
              <div className="flex items-start gap-3">
                <span className="flex-shrink-0 w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-apple-footnote font-medium">1</span>
                <p className="text-apple-callout text-muted-foreground">Fill your API key or URLs in API Access</p>
              </div>
              <div className="flex items-start gap-3">
                <span className="flex-shrink-0 w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-apple-footnote font-medium">2</span>
                <p className="text-apple-callout text-muted-foreground">Exit settings, then use the persona selector to choose your model and start chatting</p>
              </div>
              <div className="text-apple-footnote text-muted-foreground mt-2 ml-9 italic">
                Note: You can change other settings now or later. Have fun!
              </div>
            </div>
            <Button
              variant="default"
              className="bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg px-6 py-2 text-apple-callout font-medium"
              onClick={() => {
                setShowWarning(false);
              }}
            >
              Get Started
            </Button>
          </div>
        </div>
      )}

      <div className="flex flex-col gap-6">
        <ConnectCard />
        <ModelSettingsCard />
        <PersonaCard />
        <PageContextCard />
        <WebSearchCard />
        <div className="pointer-events-none h-8" />
      </div>
    </div>
  );
};
