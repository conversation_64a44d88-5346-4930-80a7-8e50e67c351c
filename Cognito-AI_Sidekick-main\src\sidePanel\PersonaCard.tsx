import { useEffect, useState, ChangeEvent, Dispatch, SetStateAction } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { IoAdd, IoTrashOutline } from './icons/index';
import React from 'react';
import { useConfig } from './ConfigContext';
import { cn } from "@/src/background/util";
import { Textarea } from "@/components/ui/textarea";

const SaveButtons = ({
  hasChange,
  onSave,
  onSaveAs,
  onCancel,
}: {
  hasChange: boolean;
  onSave: () => void;
  onSaveAs: () => void;
  onCancel: () => void;
}) => {
  if (!hasChange) return null;

  return (
    <div className="flex mt-4 space-x-2 justify-end w-full">
      <Button
        variant="default"
        size="sm"
        onClick={onSave}
        className="bg-primary text-primary-foreground hover:bg-primary/90"
      >
        Save
      </Button>
      <Button
        variant="outline"
        size="sm"
        onClick={onSaveAs}
      >
        Save As...
      </Button>
      <Button
        variant="outline"
        size="sm"
        onClick={onCancel}
      >
        Cancel
      </Button>
    </div>
  );
};

const PersonaModal = ({
  isOpen, onOpenChange, personaPrompt, personas, updateConfig, onModalClose
}: {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  personaPrompt: string;
  personas: Record<string, string>;
  updateConfig: (config: any) => void;
  onModalClose: () => void;
}) => {
  const [name, setName] = useState('');

  const handleCreate = () => {
    if (!name.trim()) return;
    updateConfig({
      personas: { ...personas, [name.trim()]: personaPrompt },
      persona: name.trim()
    });
    setName('');
    onModalClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]" onCloseAutoFocus={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle>Create New Persona</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <Label htmlFor="persona-name" className="text-base font-medium text-foreground sr-only">
            Persona Name
          </Label>
          <Input
            id="persona-name"
            placeholder="Enter persona name"
            value={name}
            onChange={e => setName(e.target.value)}
          />
        </div>
        <DialogFooter className="sm:justify-end">
          <Button type="button" variant="outline" size="sm" onClick={onModalClose}>
            Cancel
          </Button>
          <Button type="button" variant="default" size="sm" disabled={!name.trim()} onClick={handleCreate}>
            Create
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

const DeleteModal = ({
  isOpen, onOpenChange, persona, personas, updateConfig, onModalClose
}: {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  persona: string;
  personas: Record<string, string>;
  updateConfig: (config: any) => void;
  onModalClose: () => void;
}) => {
  const handleDelete = () => {
    const newPersonas = { ...personas };
    delete newPersonas[persona];
    const remainingPersonas = Object.keys(newPersonas);
    updateConfig({
      personas: newPersonas,
      persona: remainingPersonas.length > 0 ? remainingPersonas[0] : 'Scholar'
    });
    onModalClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Delete "{persona}"</DialogTitle>
          <DialogDescription className="pt-2">
            Are you sure you want to delete this persona? This action cannot be undone.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="sm:justify-end pt-4">
          <Button type="button" variant="outline" size="sm" onClick={onModalClose}>
            Cancel
          </Button>
          <Button type="button" variant="destructive" size="sm" onClick={handleDelete}>
            Delete
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

const PersonaSelect = ({
  personas, persona, updateConfig
}: {
  personas: Record<string, string>;
  persona: string;
  updateConfig: (config: any) => void;
}) => {
  return (
    <Select
      value={persona}
      onValueChange={(value) => updateConfig({ persona: value })}
    >
      <SelectTrigger className="flex w-full">
        <SelectValue placeholder="Select persona" />
      </SelectTrigger>
      <SelectContent>
        {Object.keys(personas).map((p) => (
          <SelectItem key={p} value={p}>
            {p}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

const PersonaTextareaWrapper = ({
  personaPrompt, setPersonaPrompt, isEditing, setIsEditing
}: {
  personaPrompt: string;
  setPersonaPrompt: (value: string) => void;
  isEditing: boolean;
  setIsEditing: Dispatch<SetStateAction<boolean>>;
}) => {
  const onFocusProp = {
    onFocus: (e: React.FocusEvent<HTMLTextAreaElement>) => {
      if (!isEditing) setIsEditing(true);
    },
  };

  return (
    <Textarea
      value={personaPrompt}
      onChange={(e: ChangeEvent<HTMLTextAreaElement>) => {
        if (!isEditing) setIsEditing(true);
        setPersonaPrompt(e.target.value);
      }}
      readOnly={!isEditing}
      {...onFocusProp}
      placeholder="Define the persona's characteristics and instructions here..."
      className={cn(
        "w-full min-h-[120px] resize-none",
        !isEditing
          ? "opacity-75 cursor-default"
          : "hover:border-primary focus:border-primary",
      )}
      rows={5}
    />
  );
};

export const PersonaCard = () => {
  const { config, updateConfig } = useConfig();

  const [isPersonaModalOpen, setIsPersonaModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isEditingPersona, setIsEditingPersona] = useState(false);

  const personas = config?.personas || { Scholar: "You are The Scholar, an analytical academic researcher specializing in web search analysis." };
  const currentPersonaName = config?.persona || 'Scholar';

  const defaultPromptForCurrentPersona = personas?.[currentPersonaName] ?? personas?.Scholar ?? "You are The Scholar, an analytical academic researcher specializing in web search analysis.";
  const [personaPrompt, setPersonaPrompt] = useState(defaultPromptForCurrentPersona);

  const hasChange = isEditingPersona && personaPrompt !== defaultPromptForCurrentPersona;

  useEffect(() => {
    const newDefaultPrompt = personas?.[currentPersonaName] ?? personas?.Scholar ?? "";
    setPersonaPrompt(newDefaultPrompt);
    setIsEditingPersona(false);
  }, [currentPersonaName, JSON.stringify(personas)]);

  const handlePersonaModalOpenChange = (open: boolean) => {
    setIsPersonaModalOpen(open);
    if (!open) {
      setPersonaPrompt(defaultPromptForCurrentPersona);
      setIsEditingPersona(false);
    }
  };

  const handleOpenPersonaModalForCreate = () => {
    setPersonaPrompt('');
    setIsEditingPersona(true);
    setIsPersonaModalOpen(true);
  };
  
  const handleOpenPersonaModalForSaveAs = () => {
    setIsPersonaModalOpen(true);
  };

  return (
    <div className={cn(
      "settings-card",
      "bg-card border border-border rounded-xl shadow-sm",
      "hover:shadow-md hover:border-border/80",
      "overflow-hidden"
    )}>
      <div className={cn(
        "settings-card-header",
        "px-6 py-4 border-b border-border/50"
      )}>
        <div>
          <h3 className="text-apple-title3 font-semibold text-foreground">Persona</h3>
          <p className="text-apple-footnote text-muted-foreground">Customize AI personality and behavior</p>
        </div>
      </div>

      <div className="p-6 space-y-4">
        <div className="flex items-center gap-2">
          <div className="flex-1">
            <PersonaSelect
              persona={currentPersonaName}
              personas={personas}
              updateConfig={updateConfig}
            />
          </div>
          <Button variant="ghost" size="sm" aria-label="Add new persona"
            className="p-2 h-10 w-10"
            onClick={handleOpenPersonaModalForCreate}
          >
            <IoAdd className="h-5 w-5" />
          </Button>
          {Object.keys(personas).length > 1 && (
            <Button variant="ghost" size="sm" aria-label="Delete current persona"
              className="p-2 h-10 w-10 hover:text-destructive hover:bg-destructive/10"
              onClick={() => setIsDeleteModalOpen(true)}
            >
              <IoTrashOutline className="h-5 w-5" />
            </Button>
          )}
        </div>

        <PersonaTextareaWrapper
          personaPrompt={personaPrompt}
          setPersonaPrompt={setPersonaPrompt}
          isEditing={isEditingPersona}
          setIsEditing={setIsEditingPersona}
        />

        <SaveButtons
          hasChange={hasChange}
          onSave={() => {
            updateConfig({ personas: { ...personas, [currentPersonaName]: personaPrompt } });
            setIsEditingPersona(false);
          }}
          onSaveAs={handleOpenPersonaModalForSaveAs}
          onCancel={() => {
            setPersonaPrompt(defaultPromptForCurrentPersona);
            setIsEditingPersona(false);
          }}
        />
      </div>

      <PersonaModal
        isOpen={isPersonaModalOpen}
        onOpenChange={handlePersonaModalOpenChange}
        personaPrompt={personaPrompt}
        personas={personas}
        updateConfig={updateConfig}
        onModalClose={() => setIsPersonaModalOpen(false)}
      />
      <DeleteModal
        isOpen={isDeleteModalOpen}
        onOpenChange={setIsDeleteModalOpen}
        persona={currentPersonaName}
        personas={personas}
        updateConfig={updateConfig}
        onModalClose={() => setIsDeleteModalOpen(false)}
      />
    </div>
  );
};
