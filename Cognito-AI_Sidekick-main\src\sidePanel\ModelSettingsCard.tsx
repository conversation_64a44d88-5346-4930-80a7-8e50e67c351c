import { useConfig } from './ConfigContext';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { cn } from '@/src/background/util';

type ModelParamKey = 'temperature' | 'maxTokens' | 'topP' | 'presencepenalty';

export const ModelSettingsCard = () => {
  const { config, updateConfig } = useConfig();

  const handleChange = (key: ModelParamKey) => (val: number | number[]) => {
    const valueToSet = Array.isArray(val) ? val[0] : val;
    updateConfig({ [key]: valueToSet });
  };

  const temperature = config.temperature ?? 0.7;
  const maxTokens = config.maxTokens ?? 32048;
  const topP = config.topP ?? 0.95;
  const presence_penalty = config.presencepenalty ?? 0;

  return (
    <div
      className={cn(
        'settings-card',
        'bg-card border border-border rounded-xl shadow-sm',
        'hover:shadow-md hover:border-border/80',
        'overflow-hidden',
      )}
    >
      <div className={cn('settings-card-header', 'px-6 py-4 border-b border-border/50')}>
        <div className="text-content">
          <h3 className='text-apple-title3 font-semibold text-foreground'>Model Parameters</h3>
          <p className='text-apple-footnote text-muted-foreground'>Fine-tune AI model behavior</p>
        </div>
      </div>

      <div className='p-6 space-y-6'>
        <div className='space-y-3'>
          <div className='flex items-center justify-between'>
            <Label htmlFor='temperature' className='text-apple-body font-medium text-foreground'>
              Temperature
            </Label>
            <span className='text-apple-footnote text-muted-foreground font-mono'>
              {temperature.toFixed(2)}
            </span>
          </div>
          <Slider
            id='temperature'
            min={0}
            max={2}
            step={0.01}
            value={[temperature]}
            onValueChange={handleChange('temperature')}
            className='w-full'
          />
          <p className='text-apple-caption2 text-muted-foreground'>
            Controls randomness. Lower values make responses more focused and deterministic.
          </p>
        </div>

        <div className='space-y-3'>
          <div className='flex items-center justify-between'>
            <Label htmlFor='maxTokens' className='text-apple-body font-medium text-foreground'>
              Max Tokens
            </Label>
            <span className='text-apple-footnote text-muted-foreground font-mono'>
              {maxTokens.toLocaleString()}
            </span>
          </div>
          <Input
            id='maxTokens'
            type='number'
            value={maxTokens}
            onChange={(e) => handleChange('maxTokens')(parseInt(e.target.value) || 0)}
            className='w-full'
            min={1}
            max={100000}
          />
          <p className='text-apple-caption2 text-muted-foreground'>
            Maximum length of the response in tokens.
          </p>
        </div>

        <div className='space-y-3'>
          <div className='flex items-center justify-between'>
            <Label htmlFor='topP' className='text-apple-body font-medium text-foreground'>
              Top P
            </Label>
            <span className='text-apple-footnote text-muted-foreground font-mono'>
              {topP.toFixed(2)}
            </span>
          </div>
          <Slider
            id='topP'
            min={0}
            max={1}
            step={0.01}
            value={[topP]}
            onValueChange={handleChange('topP')}
            className='w-full'
          />
          <p className='text-apple-caption2 text-muted-foreground'>
            Controls diversity via nucleus sampling. Lower values focus on more likely tokens.
          </p>
        </div>

        <div className='space-y-3'>
          <div className='flex items-center justify-between'>
            <Label
              htmlFor='presencePenalty'
              className='text-apple-body font-medium text-foreground'
            >
              Presence Penalty
            </Label>
            <span className='text-apple-footnote text-muted-foreground font-mono'>
              {presence_penalty.toFixed(2)}
            </span>
          </div>
          <Slider
            id='presencePenalty'
            min={-2}
            max={2}
            step={0.01}
            value={[presence_penalty]}
            onValueChange={handleChange('presencepenalty')}
            className='w-full'
          />
          <p className='text-apple-caption2 text-muted-foreground'>
            Reduces repetition by penalizing tokens that have already appeared.
          </p>
        </div>
      </div>
    </div>
  );
};
